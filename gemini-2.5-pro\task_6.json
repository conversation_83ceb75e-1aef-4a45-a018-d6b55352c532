{"task_id": "6", "parameter_ranges": {"frontal_overlap": {"min": 86.0, "max": 90.0}, "lateral_overlap": {"min": 76.0, "max": 80.0}, "gsd": {"min": 0.8, "max": 2.0}}, "professional_analysis": {"task_id": "6", "description": "Execute a high-precision 3D reconstruction scan of the teaching building's facade. Ensure complete data integrity for archival purposes.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_2", "target_area_name": "teaching_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18024313436918, 91.68204259872437], [52.180569959852235, 91.68287944793703], [52.17979537294124, 91.68333005905153], [52.179399887214934, 91.6825683116913], [52.18024313436918, 91.68204259872437]]]}, "parameters": {"wtask": 0.8, "wsemantic": 0.9, "height": 20, "frontal_overlap": 87.6, "lateral_overlap": 77.6, "gsd": 1.39}}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4677, "completion_tokens": 1950, "total_tokens": 6627, "cost": 0.02534625, "cached_tokens": 0}, "duration_seconds": 18.305, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:14:21"}}, "non_professional_analysis": {"task_id": "6", "description": "We need to create a digital model of this teaching building for our archives, so every facade and every corner must be captured. Nothing can be missed, to ensure the final model is both complete and accurate.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_2", "target_area_name": "teaching_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18024313436918, 91.68204259872437], [52.180569959852235, 91.68287944793703], [52.17979537294124, 91.68333005905153], [52.179399887214934, 91.6825683116913], [52.18024313436918, 91.68204259872437]]]}, "parameters": {"wtask": 0.7, "wsemantic": 0.8, "height": 20, "frontal_overlap": 86.1, "lateral_overlap": 76.1, "gsd": 1.75}}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4707, "completion_tokens": 2095, "total_tokens": 6802, "cost": 0.02683375, "cached_tokens": 0}, "duration_seconds": 20.035, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:14:39"}}, "description_en": "Perform a comprehensive scan of the teaching building exterior.", "task_usage_summary": {"total_calls": 2, "total_prompt_tokens": 9384, "total_completion_tokens": 4045, "total_tokens": 13429, "total_cost": 0.052180000000000004, "total_duration_seconds": 38.34, "model": "google/gemini-2.5-pro", "analysis_timestamp": "2025-09-26 17:14:59"}}