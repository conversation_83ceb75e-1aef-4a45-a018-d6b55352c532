{"task_id": "5", "parameter_ranges": {"frontal_overlap": {"min": 77.0, "max": 81.0}, "lateral_overlap": {"min": 67.0, "max": 71.0}, "gsd": {"min": 2.9, "max": 4.1}}, "professional_analysis": {"task_id": "5", "description": "Perform an orthomosaic survey of the basketball courts to generate an ultra-high-precision map with a GSD no worse than 1.6 cm/pixel, intended for the fine analysis of ground line wear.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_8", "target_area_name": "basketball_courts", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18579146905999, 91.67681765556335], [52.185804369161644, 91.67740237712862], [52.185270955633385, 91.67740774154665], [52.18525805544227, 91.67681229114534], [52.18579146905999, 91.67681765556335]]]}, "parameters": {"gsd": 5.02, "wtask": -0.8, "wsemantic": 0.9, "frontal_overlap": 72.4, "lateral_overlap": 62.4}, "error": null}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4715, "completion_tokens": 2118, "total_tokens": 6833, "cost": 0.02707375, "cached_tokens": 0}, "duration_seconds": 19.825, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:13:43"}}, "non_professional_analysis": {"task_id": "5", "description": "We need to issue a formal report on the basketball court's maintenance, and we need the absolute clearest photo as a base map. It must be sharp enough to see every single scratch and the tiniest crack on the ground.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Survey task"], "subtasks": [{"subtask_id": "1", "type": "Survey task", "target_area_id": "area_8", "target_area_name": "basketball_courts", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.18579146905999, 91.67681765556335], [52.185804369161644, 91.67740237712862], [52.185270955633385, 91.67740774154665], [52.18525805544227, 91.67681229114534], [52.18579146905999, 91.67681765556335]]]}, "parameters": {"wtask": -0.8, "wsemantic": 1.0, "frontal_overlap": 73.0, "lateral_overlap": 63.0, "gsd": 4.88}}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4717, "completion_tokens": 1895, "total_tokens": 6612, "cost": 0.02484625, "cached_tokens": 0}, "duration_seconds": 17.866, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:14:03"}}, "description_en": "Create a detailed map of the basketball courts.", "task_usage_summary": {"total_calls": 2, "total_prompt_tokens": 9432, "total_completion_tokens": 4013, "total_tokens": 13445, "total_cost": 0.05192, "total_duration_seconds": 37.691, "model": "google/gemini-2.5-pro", "analysis_timestamp": "2025-09-26 17:14:21"}}