#!/usr/bin/env python3
"""
使用统计分析器 - 分析和汇总LLM使用统计信息
"""

import json
import os
import glob
from typing import Dict, List, Any
import argparse


class UsageStatsAnalyzer:
    """分析任务分析结果中的LLM使用统计信息"""
    
    def __init__(self, task_analysis_dir: str):
        """
        初始化分析器
        
        Args:
            task_analysis_dir: 任务分析结果目录
        """
        self.task_analysis_dir = task_analysis_dir
    
    def load_task_files(self) -> List[Dict]:
        """
        加载所有任务分析文件
        
        Returns:
            任务数据列表
        """
        task_files = glob.glob(os.path.join(self.task_analysis_dir, "task_*.json"))
        tasks = []
        
        for file_path in task_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)
                    tasks.append(task_data)
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
        
        return tasks
    
    def analyze_usage_stats(self, tasks: List[Dict]) -> Dict[str, Any]:
        """
        分析所有任务的使用统计信息
        
        Args:
            tasks: 任务数据列表
            
        Returns:
            汇总的使用统计信息
        """
        total_stats = {
            "total_tasks": len(tasks),
            "total_llm_calls": 0,
            "total_prompt_tokens": 0,
            "total_completion_tokens": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "total_duration_seconds": 0.0,
            "average_cost_per_task": 0.0,
            "average_duration_per_task": 0.0,
            "average_tokens_per_task": 0.0,
            "task_details": []
        }
        
        for task in tasks:
            task_id = task.get("task_id", "unknown")
            task_summary = task.get("task_usage_summary", {})
            
            if task_summary:
                total_stats["total_llm_calls"] += task_summary.get("total_calls", 0)
                total_stats["total_prompt_tokens"] += task_summary.get("total_prompt_tokens", 0)
                total_stats["total_completion_tokens"] += task_summary.get("total_completion_tokens", 0)
                total_stats["total_tokens"] += task_summary.get("total_tokens", 0)
                total_stats["total_cost"] += task_summary.get("total_cost", 0.0)
                total_stats["total_duration_seconds"] += task_summary.get("total_duration_seconds", 0.0)
                
                # 记录每个任务的详细信息
                task_detail = {
                    "task_id": task_id,
                    "description_en": task.get("description_en", ""),
                    "llm_calls": task_summary.get("total_calls", 0),
                    "total_tokens": task_summary.get("total_tokens", 0),
                    "cost": task_summary.get("total_cost", 0.0),
                    "duration_seconds": task_summary.get("total_duration_seconds", 0.0),
                    "model": task_summary.get("model", ""),
                    "timestamp": task_summary.get("analysis_timestamp", "")
                }
                total_stats["task_details"].append(task_detail)
        
        # 计算平均值
        if total_stats["total_tasks"] > 0:
            total_stats["average_cost_per_task"] = round(total_stats["total_cost"] / total_stats["total_tasks"], 6)
            total_stats["average_duration_per_task"] = round(total_stats["total_duration_seconds"] / total_stats["total_tasks"], 3)
            total_stats["average_tokens_per_task"] = round(total_stats["total_tokens"] / total_stats["total_tasks"], 1)
        
        # 四舍五入总计
        total_stats["total_cost"] = round(total_stats["total_cost"], 6)
        total_stats["total_duration_seconds"] = round(total_stats["total_duration_seconds"], 3)
        
        return total_stats
    
    def generate_report(self, output_file: str = None) -> Dict[str, Any]:
        """
        生成使用统计报告
        
        Args:
            output_file: 输出文件路径（可选）
            
        Returns:
            使用统计报告
        """
        print(f"分析目录: {self.task_analysis_dir}")
        
        # 加载任务数据
        tasks = self.load_task_files()
        print(f"找到 {len(tasks)} 个任务文件")
        
        if not tasks:
            print("没有找到任务文件")
            return {}
        
        # 分析使用统计
        stats = self.analyze_usage_stats(tasks)
        
        # 打印报告
        print("\n" + "="*60)
        print("LLM 使用统计报告")
        print("="*60)
        print(f"总任务数: {stats['total_tasks']}")
        print(f"总LLM调用次数: {stats['total_llm_calls']}")
        print(f"总Token数: {stats['total_tokens']:,}")
        print(f"  - 提示Token: {stats['total_prompt_tokens']:,}")
        print(f"  - 完成Token: {stats['total_completion_tokens']:,}")
        print(f"总成本: ${stats['total_cost']:.6f}")
        print(f"总耗时: {stats['total_duration_seconds']:.3f} 秒")
        print(f"平均每任务成本: ${stats['average_cost_per_task']:.6f}")
        print(f"平均每任务耗时: {stats['average_duration_per_task']:.3f} 秒")
        print(f"平均每任务Token数: {stats['average_tokens_per_task']:.1f}")
        
        print("\n" + "-"*60)
        print("各任务详细信息:")
        print("-"*60)
        for task_detail in stats['task_details']:
            print(f"任务 {task_detail['task_id']}: "
                  f"${task_detail['cost']:.6f}, "
                  f"{task_detail['duration_seconds']:.3f}s, "
                  f"{task_detail['total_tokens']} tokens")
        
        # 保存到文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            print(f"\n报告已保存到: {output_file}")
        
        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析LLM使用统计信息')
    parser.add_argument('--task_dir', default='gemini-2.5-pro', help='任务分析结果目录')
    parser.add_argument('--output', help='输出报告文件路径（可选）')
    
    args = parser.parse_args()
    
    # 创建分析器并生成报告
    analyzer = UsageStatsAnalyzer(args.task_dir)
    analyzer.generate_report(args.output)


if __name__ == "__main__":
    main()
