{"mission_prior": {"home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "target_areas": [{"id": "area_1", "name": "library", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.178187687906515, 91.68101263046265], [52.17856612739797, 91.68177437782289], [52.17805834996039, 91.68223571777345], [52.17766273150595, 91.68148469924928]]}, "properties": {"height": 15}}, {"id": "area_2", "name": "teaching_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.18024313436918, 91.68204259872437], [52.180569959852235, 91.68287944793703], [52.17979537294124, 91.68333005905153], [52.179399887214934, 91.6825683116913]]}, "properties": {"height": 20}}, {"id": "area_3", "name": "laboratory_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.181799544748664, 91.6798861026764], [52.18178233279426, 91.68063712120056], [52.181275563282455, 91.68061566352846], [52.181257484665764, 91.6798861026764]]}, "properties": {"height": 18}}, {"id": "area_4", "name": "dormitory_area", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.184655918509605, 91.6771663427353], [52.18466444224811, 91.67742383480073], [52.18447501200689, 91.6774184703827], [52.18447067549101, 91.67715561389925]]}, "properties": {"height": 9}}, {"id": "area_5", "name": "cafeteria", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.18353694927753, 91.67615246772768], [52.18351974863444, 91.67742919921876], [52.18295181578062, 91.67743992805482], [52.18295181578062, 91.67614173889162]]}, "properties": {"height": 12}}, {"id": "area_6", "name": "track_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18701182991439, 91.67929601669313], [52.18700057924048, 91.68035817146303], [52.18537879391133, 91.68037962913515], [52.1853916940822, 91.67927992343904]]}, "properties": {}}, {"id": "area_7", "name": "football_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18675374050383, 91.6795427799225], [52.186746786132424, 91.68018651008607], [52.18558958016828, 91.68018651008607], [52.18560097667821, 91.67953205108644]]}, "properties": {}}, {"id": "area_8", "name": "basketball_courts", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18579146905999, 91.67681765556335], [52.185804369161644, 91.67740237712862], [52.185270955633385, 91.67740774154665], [52.18525805544227, 91.67681229114534]]}, "properties": {}}, {"id": "area_9", "name": "yingxue_lake", "type": "water", "geometry": {"type": "polygon", "coordinates": [[52.182737027923125, 91.67796564102174], [52.18272842751261, 91.67910289764406], [52.181420775275065, 91.67902779579164], [52.181425275805104, 91.67798709869386]]}, "properties": {}}, {"id": "area_10", "name": "main_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[52.18446610232326, 91.67749893665315], [52.18295674948406, 91.67763841152193], [52.18293094832333, 91.67923164367677]]}, "properties": {"width": 8}}, {"id": "area_11", "name": "campus_entrance", "type": "landmark", "geometry": {"type": "polygon", "coordinates": [[52.18380008507551, 91.67593252658844], [52.18415272897228, 91.67593252658844], [52.184152721690644, 91.67609882354736], [52.18380420682054, 91.67609882354736]]}, "properties": {}}]}, "user_requirements": [{"task_id": "1", "chinese_descriptions": {"description_cn": "检查图书馆屋顶是否有水渍损坏或结构性问题。", "professional": "对图书馆屋顶进行高精度结构化巡检，用于生成详细的损伤评估报告，需要能够清晰分辨微小裂缝和潜在渗漏点。", "non_professional": "最近雨季要来了，我们必须出一份详细的图书馆屋顶状况报告存档。你飞上去把每个角落都拍到，我要能放大看清楚哪怕是最细小的裂纹在不在。"}, "descriptions": {"description_en": "Inspect the library roof for any water damage or structural issues.", "professional": "Conduct a high-precision structural inspection of the library roof to generate a detailed damage assessment report, requiring clear identification of micro-cracks and potential leakage points.", "non_professional": "The rainy season is coming, so we must file a detailed report on the condition of the library's roof. You need to fly up there, capture every corner, and I need to be able to zoom in to see even the tiniest cracks."}, "frontal_overlap": {"min": 84.0, "max": 88.0}, "lateral_overlap": {"min": 74.0, "max": 78.0}, "gsd": {"min": 1.0, "max": 2.2}}, {"task_id": "2", "chinese_descriptions": {"description_cn": "检查足球场和田径跑道的状况，找出任何需要维护的区域。", "professional": "对足球场草坪和田径跑道塑胶面层进行全面的健康度评估，识别并精确标注出所有需要维护的区域，用于制定详细的年度维保计划。", "non_professional": "年底要报预算了，你帮我把跑道和足球场细细地查一遍，草皮哪里秃了，跑道哪里坏了，都要标出来，不然明年没法跟领导要钱修。"}, "descriptions": {"description_en": "Check the condition of the football field and track to identify any areas needing maintenance.", "professional": "Perform a comprehensive health assessment of the football field turf and track surface, identifying and precisely annotating all areas requiring maintenance for the annual detailed maintenance plan.", "non_professional": "It's time to report the year-end budget. Can you help me survey the running track and the football field in detail? Mark out any bald patches on the grass or damage on the track, otherwise, I can't ask management for money to fix it next year."}, "frontal_overlap": {"min": 72.0, "max": 76.0}, "lateral_overlap": {"min": 62.0, "max": 66.0}, "gsd": {"min": 4.1, "max": 5.3}}, {"task_id": "3", "chinese_descriptions": {"description_cn": "调查宿舍区，评估宿舍区和周边设施。", "professional": "对宿舍区进行快速勘察，整体评估宿舍区及周边公共设施的初步情况，形成一份概览报告。", "non_professional": "下午开会要用，你赶紧飞去宿舍区那边拍几张照片，大概看看周围有没有大问题就行，不用太细致，给我个大概印象。"}, "descriptions": {"description_en": "Survey the dormitory area to assess the dormitory area and surrounding facilities.", "professional": "Conduct a rapid survey of the dormitory area to assess the initial condition of the dormitory area and surrounding public facilities, generating an overview report.", "non_professional": "I need this for a meeting this afternoon. Can you quickly fly over to the dorms and just snap a few photos? Just a rough look to see if there are any major issues with the buildings and roads is fine. No need for detail, just give me a general idea."}, "frontal_overlap": {"min": 74.0, "max": 78.0}, "lateral_overlap": {"min": 64.0, "max": 68.0}, "gsd": {"min": 3.5, "max": 4.7}}, {"task_id": "4", "chinese_descriptions": {"description_cn": "拍摄映雪湖的高清照片，监测水位和周围植被。", "professional": "采集映雪湖的高分辨率影像，用于精确分析当前水位线、水体富营养化程度以及植被的覆盖率和健康状况。", "non_professional": "这个项目的数据很重要，我们要分析湖水和周边植被的关系。你飞过去，把湖水颜色、水位高低、植被情况都拍得清清楚楚的。"}, "descriptions": {"description_en": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation.", "professional": "Acquire high-resolution imagery of Yingxue Lake for precise analysis of the current water line, water eutrophication levels, and vegetation coverage and health.", "non_professional": "The data for this project is critical; we need to analyze the relationship between the lake water and the surrounding vegetation. Fly over there and capture the water color, water level, and the condition of the plants."}, "frontal_overlap": {"min": 72.0, "max": 76.0}, "lateral_overlap": {"min": 62.0, "max": 66.0}, "gsd": {"min": 4.1, "max": 5.3}}, {"task_id": "5", "chinese_descriptions": {"description_cn": "创建篮球场的详细地图。", "professional": "对篮球场进行正射影像航测，用于地面划线磨损情况的精细分析。", "non_professional": "我们要出一份关于篮球场地面维护的正式报告，需要一张最最清晰的照片当底图。必须能看清楚地面上每一道划痕和最细小的裂纹。"}, "descriptions": {"description_en": "Create a detailed map of the basketball courts.", "professional": "Perform an orthomosaic survey of the basketball courts to generate an ultra-high-precision map with a GSD no worse than 1.6 cm/pixel, intended for the fine analysis of ground line wear.", "non_professional": "We need to issue a formal report on the basketball court's maintenance, and we need the absolute clearest photo as a base map. It must be sharp enough to see every single scratch and the tiniest crack on the ground."}, "frontal_overlap": {"min": 77.0, "max": 81.0}, "lateral_overlap": {"min": 67.0, "max": 71.0}, "gsd": {"min": 2.9, "max": 4.1}}, {"task_id": "6", "chinese_descriptions": {"description_cn": "对教学楼外部进行全面扫描", "professional": "执行教学楼外立面的高精度三维重建扫描任务，确保数据采集的完整性，用于建筑存档。", "non_professional": "我们要把这栋教学楼做个数字模型存档，所以每个面、每个角落都必须拍到，不能有任何遗漏，要保证最后拼出来的模型是完整精确的。"}, "descriptions": {"description_en": "Perform a comprehensive scan of the teaching building exterior.", "professional": "Execute a high-precision 3D reconstruction scan of the teaching building's facade. Ensure complete data integrity for archival purposes.", "non_professional": "We need to create a digital model of this teaching building for our archives, so every facade and every corner must be captured. Nothing can be missed, to ensure the final model is both complete and accurate."}, "frontal_overlap": {"min": 86.0, "max": 90.0}, "lateral_overlap": {"min": 76.0, "max": 80.0}, "gsd": {"min": 0.8, "max": 2.0}}, {"task_id": "7", "chinese_descriptions": {"description_cn": "沿着主干道飞行，拍摄照片以记录校园交通流量。", "professional": "执行一次沿主干道的快速通过式飞行，粗略记录高峰期的交通流量，仅需获取大致车流分布情况，无需精细识别。", "non_professional": "就是想看看晚高峰的时候学校主干道堵不堵，你飞快点从头到尾拉一遍就行，让我有个大概感觉，不用一张张仔细拍。"}, "descriptions": {"description_en": "Fly along the main road and capture photos to document campus traffic flow.", "professional": "Execute a rapid fly-through along the main road to roughly document peak-hour traffic flow. The objective is to capture the general traffic distribution without need for fine-detail recognition.", "non_professional": "I just want to see how congested the main road gets during the evening rush hour. Just do a quick pass from one end to the other. I just need a general idea, no need to take careful shots."}, "frontal_overlap": {"min": 63.0, "max": 67.0}, "lateral_overlap": {"min": 53.0, "max": 57.0}, "gsd": {"min": 6.2, "max": 7.4}}, {"task_id": "8", "chinese_descriptions": {"description_cn": "从多个角度拍摄实验楼，记录其当前状态。", "professional": "对实验楼进行多角度、多高度的环绕飞行拍摄，获取一组高质量的影像资料，全面记录建筑当前的外观状态，用于提交给工程部。", "non_professional": "工程部那边要实验楼的现状资料，你帮忙把楼的四周都拍一遍，拍清楚点，保证信息是完整的，别让他们说我们交的材料不全。"}, "descriptions": {"description_en": "Photograph the laboratory building from multiple angles to document its current state.", "professional": "Capture a set of high-quality imagery of the laboratory building via multi-angle, multi-altitude orbital flights to comprehensively document the building's current external condition for submission to the engineering department.", "non_professional": "The engineering department needs a status report on the lab building. Please help me get shots of all sides of the building, make them clear, and ensure the information is complete so they can't say our submission is inadequate."}, "frontal_overlap": {"min": 82.0, "max": 86.0}, "lateral_overlap": {"min": 72.0, "max": 76.0}, "gsd": {"min": 1.5, "max": 2.7}}, {"task_id": "9", "chinese_descriptions": {"description_cn": "检查食堂屋顶和周围区域是否有潜在的维护问题。", "professional": "对食堂屋顶及周边附属设施（如排烟管道、空调外机）进行一次标准巡检，排查可见的潜在维护风险点，形成检查清单。", "non_professional": "又到安全检查月了，你把食堂房顶和旁边那些管道设备都检查一遍，看看有没有松动或者生锈的地方，我好填表上报。"}, "descriptions": {"description_en": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.", "professional": "Conduct a standard inspection of the cafeteria roof and its surrounding ancillary facilities (e.g., exhaust ducts, AC units) to identify visible potential maintenance risks and generate an inspection checklist.", "non_professional": "It's safety inspection month again. Can you check the cafeteria roof and all those pipes and equipment next to it? Look for anything loose or rusty so I can fill out the report form."}, "frontal_overlap": {"min": 82.0, "max": 86.0}, "lateral_overlap": {"min": 72.0, "max": 76.0}, "gsd": {"min": 1.6, "max": 2.8}}, {"task_id": "10", "chinese_descriptions": {"description_cn": "拍摄校园入口和主要道路的航拍照片，用于校园导航材料。", "professional": "为制作校园导航材料，请采集校园主入口及几条核心道路的航拍影像，要求构图规整、画面清晰，能明确展示道路走向和标志性建筑。", "non_professional": "要给新生做个好看又实用的导航图，你帮忙把学校大门和那几条重要的路拍一下，要拍得标志清晰，让人一眼就能看明白怎么走。"}, "descriptions": {"description_en": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.", "professional": "To produce campus navigation materials, please capture aerial imagery of the main campus entrance and key roads. The imagery should be well-composed and clear, explicitly showing road directions and landmark buildings.", "non_professional": "We want to create a nice and practical navigation guide for new students. Can you help me get some shots of the main gate and the main roads? They need to be clear enough for people to understand the layout at a glance."}, "frontal_overlap": {"min": 72.0, "max": 76.0}, "lateral_overlap": {"min": 62.0, "max": 66.0}, "gsd": {"min": 4.0, "max": 5.2}}]}