# LLM使用统计功能说明

## 概述

根据OpenRouter官方文档，我们已经为无人机任务分析器添加了详细的LLM使用统计功能。该功能可以跟踪每次LLM调用的成本、耗时和Token使用情况。

## 功能特性

### 1. 实时使用统计
- **Token统计**: 记录提示Token、完成Token和总Token数量
- **成本跟踪**: 记录每次调用的实际成本（以美元计）
- **耗时监控**: 记录每次API调用的响应时间
- **缓存Token**: 记录从缓存中读取的Token数量

### 2. 多级别统计
- **单次调用级别**: 每次LLM API调用的详细统计
- **任务级别**: 每个任务（包括professional和non_professional分析）的汇总统计
- **全局级别**: 所有任务的总体统计分析

## 数据结构

### 单次调用统计 (`llm_usage_stats`)
```json
{
  "llm_usage_stats": {
    "usage_stats": {
      "prompt_tokens": 4689,
      "completion_tokens": 676,
      "total_tokens": 5365,
      "cost": 0.0030967,
      "cached_tokens": 0
    },
    "duration_seconds": 3.752,
    "model": "google/gemini-2.5-flash-preview-09-2025",
    "timestamp": "2025-09-26 16:40:36"
  }
}
```

### 任务级别汇总 (`task_usage_summary`)
```json
{
  "task_usage_summary": {
    "total_calls": 2,
    "total_prompt_tokens": 9434,
    "total_completion_tokens": 2005,
    "total_tokens": 11439,
    "total_cost": 0.0078427,
    "total_duration_seconds": 10.01,
    "model": "google/gemini-2.5-flash-preview-09-2025",
    "analysis_timestamp": "2025-09-26 16:41:46"
  }
}
```

## 使用方法

### 1. 运行任务分析
```bash
# 分析单个任务
python drone_mission_analyzer_base.py --task_id 1

# 分析所有任务
python drone_mission_analyzer_base.py
```

### 2. 查看使用统计
```bash
# 生成使用统计报告
python usage_stats_analyzer.py --output usage_report.json
```

## 统计报告示例

```
============================================================
LLM 使用统计报告
============================================================
总任务数: 2
总LLM调用次数: 4
总Token数: 22,215
  - 提示Token: 18,855
  - 完成Token: 3,360
总成本: $0.014056
总耗时: 16.916 秒
平均每任务成本: $0.007028
平均每任务耗时: 8.458 秒
平均每任务Token数: 11107.5

------------------------------------------------------------
各任务详细信息:
------------------------------------------------------------
任务 1: $0.006214, 6.906s, 10776 tokens
任务 2: $0.007843, 10.010s, 11439 tokens
```

## 技术实现

### 1. API调用修改
- 在OpenAI客户端调用中添加了`extra_body`参数来启用使用统计
- 使用`time`模块记录API调用的开始和结束时间

### 2. 数据提取
- 从API响应中提取`usage`对象的详细信息
- 处理可能缺失的字段，提供默认值

### 3. 数据存储
- 在每个分析结果中添加`llm_usage_stats`字段
- 在任务级别添加`task_usage_summary`汇总字段

## 成本分析

根据当前测试结果：
- **平均每任务成本**: ~$0.007 (约0.05元人民币)
- **平均每任务耗时**: ~8.5秒
- **平均每任务Token**: ~11,000个

这些数据可以帮助：
1. **预算规划**: 估算大规模任务分析的成本
2. **性能优化**: 识别耗时较长的任务类型
3. **资源管理**: 监控Token使用效率

## 注意事项

1. **网络延迟**: 耗时统计包含网络传输时间
2. **成本精度**: 成本数据来自OpenRouter的实时计费
3. **缓存效果**: 缓存Token可以显著降低成本
4. **模型差异**: 不同模型的成本和性能差异较大

## 文件说明

- `drone_mission_analyzer_base.py`: 主分析器，已添加使用统计功能
- `usage_stats_analyzer.py`: 使用统计分析工具
- `usage_report.json`: 生成的使用统计报告
- 各任务JSON文件: 包含详细的使用统计信息
