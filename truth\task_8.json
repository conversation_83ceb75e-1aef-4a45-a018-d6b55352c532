{"task_id": "8", "parameter_ranges": {"frontal_overlap": {"min": 82.0, "max": 86.0}, "lateral_overlap": {"min": 72.0, "max": 76.0}, "gsd": {"min": 1.5, "max": 2.7}}, "professional_analysis": {"task_id": "8", "description": "Capture a set of high-quality imagery of the laboratory building via multi-angle, multi-altitude orbital flights to comprehensively document the building's current external condition for submission to the engineering department.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_3", "target_area_name": "laboratory_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.181799544748664, 91.6798861026764], [52.18178233279426, 91.68063712120056], [52.181275563282455, 91.68061566352846], [52.181257484665764, 91.6798861026764], [52.181799544748664, 91.6798861026764]]]}, "parameters": {"wtask": 0.6, "wsemantic": 0.8, "height": 18, "frontal_overlap": 85.1, "lateral_overlap": 75.1, "gsd": 1.97}, "error": null}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4703, "completion_tokens": 2458, "total_tokens": 7161, "cost": 0.03045875, "cached_tokens": 0}, "duration_seconds": 22.768, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:15:35"}}, "non_professional_analysis": {"task_id": "8", "description": "The engineering department needs a status report on the lab building. Please help me get shots of all sides of the building, make them clear, and ensure the information is complete so they can't say our submission is inadequate.", "home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "task_types": ["Structure scan task"], "subtasks": [{"subtask_id": "1", "type": "Structure scan task", "target_area_id": "area_3", "target_area_name": "laboratory_building", "feasible": true, "geometry": {"type": "Polygon", "coordinates": [[[52.181799544748664, 91.6798861026764], [52.18178233279426, 91.68063712120056], [52.181275563282455, 91.68061566352846], [52.181257484665764, 91.6798861026764], [52.181799544748664, 91.6798861026764]]]}, "parameters": {"wtask": 0.6, "wsemantic": 0.8, "height": 18, "frontal_overlap": 85.1, "lateral_overlap": 75.1, "gsd": 1.97}, "error": null}], "llm_usage_stats": {"usage_stats": {"prompt_tokens": 4715, "completion_tokens": 2006, "total_tokens": 6721, "cost": 0.02595375, "cached_tokens": 0}, "duration_seconds": 18.918, "model": "google/gemini-2.5-pro", "timestamp": "2025-09-26 17:15:58"}}, "description_en": "Photograph the laboratory building from multiple angles to document its current state.", "task_usage_summary": {"total_calls": 2, "total_prompt_tokens": 9418, "total_completion_tokens": 4464, "total_tokens": 13882, "total_cost": 0.056412500000000004, "total_duration_seconds": 41.686, "model": "google/gemini-2.5-pro", "analysis_timestamp": "2025-09-26 17:16:16"}}